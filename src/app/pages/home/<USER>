import { ChangeDetectorRef, Component } from '@angular/core';
import { IonContent } from '@ionic/angular/standalone';
import { ImageModule } from 'primeng/image';
import { InputTextModule } from 'primeng/inputtext';
import { IftaLabelModule } from 'primeng/iftalabel';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { Category } from '../../components/category/category';
import { ProductComponent } from '../../components/product/product';
import { BillingComponent } from '../../components/billing/billing';
import { ViewChild } from "@angular/core";
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HeaderComponent } from "../../components/header/header";
import { TypeSenseService } from '../../services/typesense';
@Component({
    selector: 'app-home',
    templateUrl: 'home.page.html',
    standalone: true,
    imports: [IonContent, CommonModule, FormsModule,
        ImageModule,
        InputTextModule,
        IftaLabelModule,
        ProgressSpinnerModule,
        Category,
        ProductComponent,
        BillingComponent,
        HeaderComponent
    ],
})
export class HomePage {
    @ViewChild('billingComponent') billingComponent: BillingComponent | any;
    categories: any[] = [];
    products: any[] = [];
    allProducts: any[] = [];
    
    // Infinite scroll properties
    currentPage: number = 1;
    totalPages: number = 0;
    isLoadingProducts: boolean = false;
    hasMoreProducts: boolean = true;
    selectedCategoryName: string = '';
    constructor(
        private typesenseService: TypeSenseService,
        private cdr: ChangeDetectorRef
    ) { }
    ngOnInit(): void {
        this.getCategories();
        this.getSearchProducts('');
    }

    getTotalItems(categoryName: string): number {
        const count = this.products.filter(product => {
            return product.categories && product.categories.includes(categoryName);
        }).length;
        return count;
    }

    getSearchProducts(query: string) {
        this.typesenseService.searchProducts(query).then((result) => {
            this.products = result.products || [];
        });
    }

    getCategories() {
        this.typesenseService.getCategories().then((categories) => {
            this.categories = categories || [];
            if (this.categories.length > 0) {
                this.getProductsByCategory(this.categories[0].name);
            }
        });
    }

    async getProductsByCategory(categoryName: string): Promise<void> {
        this.selectedCategoryName = categoryName;
        this.currentPage = 1;
        this.isLoadingProducts = true;

        try {
            const result = await this.typesenseService.getProductsByCategory(categoryName, 1, 30);
            console.log('Initial category result:', result);
            this.products = result.products || [];
            this.totalPages = result.totalPages || 0;
            this.hasMoreProducts = this.currentPage < this.totalPages;
            console.log('Initial load complete:', {
                productsCount: this.products.length,
                currentPage: this.currentPage,
                totalPages: this.totalPages,
                hasMoreProducts: this.hasMoreProducts
            });
        } catch (error) {
            console.error('Error loading products by category:', error);
            this.products = [];
        } finally {
            this.isLoadingProducts = false;
        }
    }
    async loadMoreProducts() {
        if (this.isLoadingProducts || !this.hasMoreProducts || !this.selectedCategoryName) {
            console.log('loadMoreProducts: Early return due to conditions');
            return;
        }
        this.isLoadingProducts = true;
        try {
            const nextPage = this.currentPage + 1;
            const result = await this.typesenseService.getProductsByCategory(this.selectedCategoryName, nextPage, 30);

            if (result.products && result.products.length > 0) {
                this.products = [...this.products, ...result.products];
                this.currentPage = nextPage;
                this.hasMoreProducts = this.currentPage < this.totalPages;
            } else {
                this.hasMoreProducts = false;
                console.log('No more products available');
            }
        } catch (error) {
            console.error('Error loading more products:', error);
        } finally {
            this.isLoadingProducts = false;
        }
    }
    onProductsScroll(event: any) {
        const element = event.target;
        const threshold = 100; // Load more when 100px from bottom
        const distanceFromBottom = element.scrollHeight - element.scrollTop - element.clientHeight;

        if (distanceFromBottom < threshold) {
            console.log('Threshold reached, calling loadMoreProducts');
            this.loadMoreProducts();
        }
    }

    trackByProductId(index: number, product: any): any {
        return product.id || index;
    }

    getCategoryWithTotalItems(categories: any): any {
        const totalItems = this.getTotalItems(categories.name);
        return {
            ...categories,
            totalItems: totalItems
        };
    }

    onImageError(event: any) {
        event.target.src = 'https://www.shorekids.co.nz/wp-content/uploads/2014/08/image-placeholder.jpg';
    }
    addToCart(product: any) {
        const cartItem = this.billingComponent?.cartItems?.find((item: any) => item.id === product.id);
        if (cartItem) {
            cartItem.quantity += 1;
        } else {
            this.billingComponent?.cartItems?.push({ ...product, quantity: 1 });
            this.cdr.detectChanges();
        }
    }
    addDefaultItemsToCart() {
        this.billingComponent.cartItems = [];
        const itemsToAdd = Math.floor(Math.random() * 10) + 1;
        const availableProducts = [...this.products];
        for (let i = 0; i < itemsToAdd; i++) {
            const randomIndex = Math.floor(Math.random() * availableProducts.length);
            const randomProduct = availableProducts[randomIndex];
            const quantity = Math.floor(Math.random() * 5) + 1;
            const checkExist = this.billingComponent?.cartItems?.find((item: any) => item.id === randomProduct.id);
            if (!checkExist) {
                this.billingComponent?.cartItems?.push({ ...randomProduct, quantity: quantity });
            }
            availableProducts.splice(randomIndex, 1);
            if (availableProducts.length === 0) {
                break;
            }
        }
    }
    addItemToCart(data: { searchText: string, event: any, keyCode: number }) {
        if (data.keyCode === 13) {
            const findItem = this.products.find((item: any) => item.name.toLowerCase() === data.searchText.toLowerCase());
            if (findItem) {
                this.billingComponent.searchText = '';
                this.addToCart(findItem);
            }
        }
    }
}